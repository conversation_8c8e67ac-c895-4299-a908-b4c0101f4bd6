﻿using System.Net;
using System.Text.Json;
using CoverGo.Payments.Application.Payments.Commands.RegisterPayment;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.Refund;
using MongoDB.Driver.Linq;
using PaymentAggregate = CoverGo.Payments.Domain.Payment.PaymentAggregate;

namespace CoverGo.Payments.Application.Payments.Services;

public interface IPaymentService
{
    IMongoQueryable<PaymentAggregate> GetPayments();
    
    Task<TokenizedPaymentInitialization> TokenizePaymentInitializationAsync(
        TokenizedPaymentInitializationAggregate tokenizedPaymentInitialization, CancellationToken cancellationToken);

    Task CancelPaymentInitializationsAsync(List<string> initializationTokens, PaymentStatus? cancellationStatus = null, string? cancellationReason = null, CancellationToken cancellationToken = default);

    Task<TokenizedPaymentInitializationAggregate?> GetTokenizedPaymentInitializationAsync(string initializationToken,
        bool throwIfNotFound, CancellationToken cancellationToken);

    Task<PaymentAggregate?> FindExistingPaymentByInvoiceAndInitializationTokenAsync(string invoiceNumber,
        string initializationToken, CancellationToken cancellationToken);

    Task<ProcessInitialPaymentResult> ProcessInitialPaymentAsync(PreauthPaymentAggregate preauthPayment,
        JsonElement? dynamicFields, CancellationToken cancellationToken = default);

    Task<PaymentAggregate> CapturePreauthPaymentAsync(string preauthPaymentId,
        CancellationToken cancellationToken = default);

    Task<PreauthPaymentAggregate> CancelPreauthPaymentAsync(string preauthPaymentId,
        PaymentStatus cancellationStatus = PaymentStatus.Canceled,
        string? cancellationReason = null,
        CancellationToken cancellationToken = default);

    Task<PreauthPaymentAggregate> FinalizePreauthPaymentAsync(string preauthPaymentId, JsonElement? dynamicFields,
        bool isWebhookCall = false,
        CancellationToken cancellationToken = default);

    Task<RecurringPaymentAggregate> ProcessRecurringPaymentAsync(decimal amount,
        int decimalPrecision,
        string currencyDesc,
        string policyId,
        string invoiceNumber,
        string payorId,
        string? renewedFromPolicyId,
        CancellationToken cancellationToken = default);

    Task<PaymentAggregate> FailPaymentAsync(string paymentId, CancellationToken cancellationToken = default);

    Task<RefundAggregate> RefundPaymentAsync(string paymentId, decimal amount, int decimalPrecision,
        CancellationToken cancellationToken = default);

    Task<(string responseStr, HttpStatusCode httpStatusCode)> HandleWebhookAsync(string webhookBody,
        PaymentProvider provider, CancellationToken cancellationToken = default);

    Task<PaymentAggregate> RegisterPaymentAsync(RegisterPaymentCommand registerPaymentCommand,
        CancellationToken cancellationToken);
}