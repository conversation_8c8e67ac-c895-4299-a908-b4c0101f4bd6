using System.Text.Json;
using AutoMapper;
using CoverGo.Payments.Application.Payments.Commands.InitializePayment;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.TokenizedPaymentInitialization;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;

namespace CoverGo.Payments.UnitTests.Payments.Commands.InitializePayment;

public class InitializePaymentCommandHandlerTests
{
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IPaymentService> _paymentServiceMock;
    private readonly Mock<ILogger<InitializePaymentCommandHandler>> _loggerMock;
    private readonly InitializePaymentCommandHandler _handler;

    public InitializePaymentCommandHandlerTests()
    {
        _mapperMock = new Mock<IMapper>();
        _paymentServiceMock = new Mock<IPaymentService>();
        _loggerMock = new Mock<ILogger<InitializePaymentCommandHandler>>();
        _handler = new InitializePaymentCommandHandler(_mapperMock.Object, _paymentServiceMock.Object, _loggerMock.Object);
    }

    [Fact]
    public async Task GIVEN_initialization_token_with_existing_payment_WHEN_Handle_THEN_should_reuse_existing_payment()
    {
        // Arrange
        var initializationToken = Guid.NewGuid().ToString();
        var invoiceNumber = "INV-12345";
        var policyId = "POL-12345";
        var payorId = "PAY-12345";

        var command = new InitializePaymentCommand(
            Amount: null,
            CurrencyCode: null,
            CurrencyDesc: null,
            DecimalPrecision: null,
            PolicyId: null,
            InvoiceNumber: null,
            PayorId: null,
            PaymentProvider: null,
            DynamicFields: null,
            InitializationToken: initializationToken
        );

        var tokenizedAggregate = new TokenizedPaymentInitializationAggregate(
            PaymentProvider.Stripe,
            1000m,
            "USD",
            "US Dollar",
            2,
            null,
            null,
            policyId,
            invoiceNumber,
            payorId,
            false
        );

        var existingPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("USD", "US Dollar", 1000m, 2),
            policyId,
            invoiceNumber,
            payorId,
            null,
            initializationToken,
            false,
            false
        );

        var expectedResult = new ProcessInitialPaymentResultDto
        {
            Payment = new PaymentDto { Id = existingPayment.Id }
        };

        // Setup for the first call in ProcessPaymentWithInitializationTokenAsync
        _paymentServiceMock
            .Setup(s => s.GetTokenizedPaymentInitializationAsync(initializationToken, true, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tokenizedAggregate);

        _paymentServiceMock
            .Setup(s => s.FindExistingPaymentAsync(policyId, invoiceNumber, initializationToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingPayment);

        var processResult = new ProcessInitialPaymentResult(existingPayment);
        _paymentServiceMock
            .Setup(s => s.ProcessInitWithExistingPaymentAsync(existingPayment, invoiceNumber, initializationToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync(processResult);

        _mapperMock
            .Setup(m => m.Map<ProcessInitialPaymentResultDto>(processResult))
            .Returns(expectedResult);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().Be(expectedResult);

        // Verify that the existing payment lookup was called
        _paymentServiceMock.Verify(
            s => s.FindExistingPaymentAsync(policyId, invoiceNumber, initializationToken, It.IsAny<CancellationToken>()),
            Times.Once);

        // Verify that ProcessInitWithExistingPaymentAsync was called
        _paymentServiceMock.Verify(
            s => s.ProcessInitWithExistingPaymentAsync(existingPayment, invoiceNumber, initializationToken, It.IsAny<CancellationToken>()),
            Times.Once);

        // Verify that ProcessInitialPaymentAsync was never called since we reused existing payment
        _paymentServiceMock.Verify(
            s => s.ProcessInitialPaymentAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<JsonElement?>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task GIVEN_initialization_token_with_no_existing_payment_WHEN_Handle_THEN_should_create_new_payment()
    {
        // Arrange
        var initializationToken = Guid.NewGuid().ToString();
        var invoiceNumber = "INV-12345";
        var policyId = "POL-12345";
        var payorId = "PAY-12345";

        var command = new InitializePaymentCommand(
            Amount: null,
            CurrencyCode: null,
            CurrencyDesc: null,
            DecimalPrecision: null,
            PolicyId: null,
            InvoiceNumber: null,
            PayorId: null,
            PaymentProvider: null,
            DynamicFields: null,
            InitializationToken: initializationToken
        );

        var tokenizedAggregate = new TokenizedPaymentInitializationAggregate(
            PaymentProvider.Stripe,
            1000m,
            "USD",
            "US Dollar",
            2,
            null,
            null,
            policyId,
            invoiceNumber,
            payorId,
            false
        );

        var newPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("USD", "US Dollar", 1000m, 2),
            policyId,
            invoiceNumber,
            payorId,
            null,
            initializationToken,
            false,
            false
        );

        var processResult = new ProcessInitialPaymentResult(newPayment);
        var expectedResult = new ProcessInitialPaymentResultDto
        {
            Payment = new PaymentDto { Id = newPayment.Id }
        };

        _paymentServiceMock
            .Setup(s => s.GetTokenizedPaymentInitializationAsync(initializationToken, true, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tokenizedAggregate);

        _paymentServiceMock
            .Setup(s => s.FindExistingPaymentAsync(policyId, invoiceNumber, initializationToken, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PaymentAggregate?)null);

        _paymentServiceMock
            .Setup(s => s.ProcessInitialPaymentAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<JsonElement?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(processResult);

        _mapperMock
            .Setup(m => m.Map<ProcessInitialPaymentResultDto>(processResult))
            .Returns(expectedResult);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().Be(expectedResult);

        _paymentServiceMock.Verify(
            s => s.FindExistingPaymentAsync(policyId, invoiceNumber, initializationToken, It.IsAny<CancellationToken>()),
            Times.Once);

        _paymentServiceMock.Verify(
            s => s.ProcessInitialPaymentAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<JsonElement?>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }

    [Fact]
    public async Task GIVEN_initialization_token_with_null_invoice_number_WHEN_Handle_THEN_should_create_new_payment_without_checking_existing()
    {
        // Arrange
        var initializationToken = Guid.NewGuid().ToString();
        var policyId = "POL-12345";
        var payorId = "PAY-12345";

        var command = new InitializePaymentCommand(
            Amount: null,
            CurrencyCode: null,
            CurrencyDesc: null,
            DecimalPrecision: null,
            PolicyId: null,
            InvoiceNumber: null,
            PayorId: null,
            PaymentProvider: null,
            DynamicFields: null,
            InitializationToken: initializationToken
        );

        var tokenizedAggregate = new TokenizedPaymentInitializationAggregate(
            PaymentProvider.Stripe,
            1000m,
            "USD",
            "US Dollar",
            2,
            null,
            null,
            policyId,
            null, // null invoice number
            payorId,
            false
        );

        var newPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("USD", "US Dollar", 1000m, 2),
            policyId,
            null,
            payorId,
            null,
            initializationToken,
            false,
            false
        );

        var processResult = new ProcessInitialPaymentResult(newPayment);
        var expectedResult = new ProcessInitialPaymentResultDto
        {
            Payment = new PaymentDto { Id = newPayment.Id }
        };

        _paymentServiceMock
            .Setup(s => s.GetTokenizedPaymentInitializationAsync(initializationToken, true, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tokenizedAggregate);

        _paymentServiceMock
            .Setup(s => s.ProcessInitialPaymentAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<JsonElement?>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(processResult);

        _mapperMock
            .Setup(m => m.Map<ProcessInitialPaymentResultDto>(processResult))
            .Returns(expectedResult);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().Be(expectedResult);

        _paymentServiceMock.Verify(
            s => s.FindExistingPaymentAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);

        _paymentServiceMock.Verify(
            s => s.ProcessInitialPaymentAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<JsonElement?>(), It.IsAny<CancellationToken>()),
            Times.Once);
    }
}
